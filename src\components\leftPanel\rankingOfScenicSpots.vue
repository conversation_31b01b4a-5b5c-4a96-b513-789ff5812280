<!-- 今日巡检情况 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>今日巡检情况</span>
        <span class="header-subtitle">INSPECTION OF TODAY</span>
      </div>
    </template>
    <template #content>
      <div class="inspection-container">
        <!-- 桥梁部分 -->
        <div class="inspection-section bridge-section">
          <div class="section-header">
            <div class="section-title">桥梁</div>
          </div>
          <div class="section-content">
            <div class="chart-container">
              <CEcharts :option="bridgeOption" class="progress-chart" />
              <div class="chart-center">
                <div class="percentage">0%</div>
                <div class="label">已巡检</div>
              </div>
            </div>
            <div class="stats-info">
              <div class="stat-item">
                <div class="stat-line green"></div>
                <span class="stat-label">已巡检</span>
                <span class="stat-value">0个</span>
              </div>
              <div class="stat-item">
                <div class="stat-line gray"></div>
                <span class="stat-label">未巡检</span>
                <span class="stat-value">58个</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 隧道部分 -->
        <div class="inspection-section tunnel-section">
          <div class="section-header">
            <div class="section-title">隧道</div>
          </div>
          <div class="section-content">
            <div class="chart-container">
              <CEcharts :option="tunnelOption" class="progress-chart" />
              <div class="chart-center">
                <div class="percentage">0%</div>
                <div class="label">已巡检</div>
              </div>
            </div>
            <div class="stats-info">
              <div class="stat-item">
                <div class="stat-line green"></div>
                <span class="stat-label">已巡检</span>
                <span class="stat-value">0个</span>
              </div>
              <div class="stat-item">
                <div class="stat-line gray"></div>
                <span class="stat-label">未巡检</span>
                <span class="stat-value">9个</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const bridgeOption = ref({})
const tunnelOption = ref({})

// 创建圆形进度图表配置
const createProgressChart = (percentage = 0, color = '#00D4FF') => {
  return {
    series: [
      {
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: percentage,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: color },
                { offset: 1, color: color + '80' }
              ])
            }
          },
          {
            value: 100 - percentage,
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        silent: true
      }
    ]
  }
}

onMounted(() => {
  // 桥梁进度图表 - 蓝色
  bridgeOption.value = createProgressChart(0, '#00D4FF')
  // 隧道进度图表 - 蓝色
  tunnelOption.value = createProgressChart(0, '#00D4FF')
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #C5D6E6;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #7A8BA3;
    margin-left: 8px;
  }
}

.inspection-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 20px 0;
}

.inspection-section {
  position: relative;
  background: url('@/assets/images/bg_panel1_top_box.png') no-repeat center center;
  background-size: 100% 100%;
  border-radius: 8px;
  padding: 20px;

  .section-header {
    margin-bottom: 16px;

    .section-title {
      position: relative;
      display: inline-block;
      font-size: 16px;
      color: #C5D6E6;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
        border-radius: 2px;
      }
    }
  }

  .section-content {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .chart-container {
    position: relative;
    width: 120px;
    height: 120px;

    .progress-chart {
      width: 100%;
      height: 100%;
    }

    .chart-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;

      .percentage {
        font-size: 24px;
        font-weight: bold;
        color: #00D4FF;
        line-height: 1;
      }

      .label {
        font-size: 12px;
        color: #C5D6E6;
        margin-top: 4px;
      }
    }
  }

  .stats-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .stat-line {
        width: 4px;
        height: 16px;
        border-radius: 2px;

        &.green {
          background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
        }

        &.gray {
          background: rgba(255, 255, 255, 0.3);
        }
      }

      .stat-label {
        font-size: 14px;
        color: #C5D6E6;
        min-width: 48px;
      }

      .stat-value {
        font-size: 14px;
        color: #00D4FF;
        font-weight: bold;
      }
    }
  }
}


</style>
