<!-- 今日巡检情况 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>今日巡检情况</span>
        <span class="header-subtitle">INSPECTION OF TODAY</span>
      </div>
    </template>
    <template #content>
      <div class="inspection-container">
        <!-- 选项卡按钮 -->
        <div class="tab-buttons">
          <div class="tab-button" :class="{ active: activeTab === 'bridge' }" @click="activeTab = 'bridge'">
            桥梁
          </div>
          <div class="tab-button" :class="{ active: activeTab === 'tunnel' }" @click="activeTab = 'tunnel'">
            隧道
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <div class="chart-container">
            <CEcharts :option="currentOption" class="progress-chart" />
            <div class="chart-center">
              <div class="percentage">0%</div>
              <div class="label">已巡检</div>
            </div>
          </div>
          <div class="stats-info">
            <div class="stat-item">
              <div class="stat-line green"></div>
              <span class="stat-label">已巡检</span>
              <span class="stat-value">{{ currentStats.inspected }}个</span>
            </div>
            <div class="stat-item">
              <div class="stat-line gray"></div>
              <span class="stat-label">未巡检</span>
              <span class="stat-value">{{ currentStats.uninspected }}个</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const activeTab = ref('bridge')
const bridgeOption = ref({})
const tunnelOption = ref({})

// 统计数据
const bridgeStats = ref({ inspected: 0, uninspected: 58 })
const tunnelStats = ref({ inspected: 0, uninspected: 9 })

// 计算当前显示的选项和统计数据
const currentOption = computed(() => {
  return activeTab.value === 'bridge' ? bridgeOption.value : tunnelOption.value
})

const currentStats = computed(() => {
  return activeTab.value === 'bridge' ? bridgeStats.value : tunnelStats.value
})

// 创建圆形进度图表配置
const createProgressChart = (percentage = 0, color = '#00D4FF') => {
  return {
    series: [
      {
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: percentage,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: color },
                { offset: 1, color: color + '80' }
              ])
            }
          },
          {
            value: 100 - percentage,
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        silent: true
      }
    ]
  }
}

onMounted(() => {
  // 桥梁进度图表 - 蓝色
  bridgeOption.value = createProgressChart(0, '#00D4FF')
  // 隧道进度图表 - 蓝色
  tunnelOption.value = createProgressChart(0, '#00D4FF')
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.inspection-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
}

.tab-buttons {
  display: flex;
  gap: 16px;
 justify-content: space-around;

  .tab-button {
    position: relative;
    padding: 8px 24px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    background: url('@/assets/images/bg_panel1_top_box.png') no-repeat center center;
    background-size: 100% 100%;
    min-width: 80px;
    text-align: center;
  }
}

.content-area {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 20px;
  // background: url('@/assets/images/bg_panel1_top_box.png') no-repeat center center;
  // background-size: 100% 100%;
  // border-radius: 8px;
}

.chart-container {
  position: relative;
  width: 120px;
  height: 120px;

  .progress-chart {
    width: 100%;
    height: 100%;
  }

  .chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .percentage {
      font-size: 24px;
      font-weight: bold;
      color: #00D4FF;
      line-height: 1;
    }

    .label {
      font-size: 12px;
      color: #C5D6E6;
      margin-top: 4px;
    }
  }
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .stat-line {
      width: 4px;
      height: 16px;
      border-radius: 2px;

      &.green {
        background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
      }

      &.gray {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .stat-label {
      font-size: 14px;
      color: #C5D6E6;
      min-width: 48px;
    }

    .stat-value {
      font-size: 14px;
      color: #00D4FF;
      font-weight: bold;
    }
  }
}
</style>
